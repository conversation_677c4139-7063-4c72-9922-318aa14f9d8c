import QtQuick
import Quickshell
import Quickshell.Io
import qs.modules.common as Common

Item {
    id: root

    // This is now a simple property that the parent file will control.
    property bool isPlaying: true
    
    property var visualizerPoints: []

    Process {
        id: cavaProc
        // This correctly uses the 'isPlaying' property from our own module.
        running: root.isPlaying
        
        onRunningChanged: {
            if (!cavaProc.running) {
                root.visualizerPoints = [];
            }
        }
        
        command: ["cava", "-p", `${Common.FileUtils.trimFileProtocol(Common.Directories.scriptPath)}/cava/raw_output_config.txt`]
        
        stdout: SplitParser {
            onRead: data => {
                let points = data.split(";").map(p => parseFloat(p.trim())).filter(p => !isNaN(p));
                root.visualizerPoints = points;
            }
        }
    }

    // --- Visuals (No changes here) ---
    width: 24
    height: 20
    Row {
        anchors.centerIn: parent; spacing: 2
        Repeater {
            model: root.visualizerPoints.length
            Rectangle {
                width: 3
                height: {
                    const point = root.visualizerPoints[index] || 0;
                    const maxHeight = 16;
                    const minHeight = 4;
                    return minHeight + (point * (maxHeight - minHeight));
                }
                radius: 1.5
                color: root.isPlaying ? "#1db954" : "#535353"
                anchors.verticalCenter: parent.verticalCenter
                Behavior on height { NumberAnimation { duration: 80; easing.type: Easing.OutCubic } }
                Behavior on color { ColorAnimation { duration: 400 } }
            }
        }
    }
}