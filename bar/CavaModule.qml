import QtQuick
import Quickshell
import Quickshell.Io
import qs.modules.common as Common

Item {
    id: root

    // This is now a simple property that the parent file will control.
    property bool isPlaying: true

    property var visualizerPoints: []

    Component.onCompleted: {
        console.log("CavaModule: Component completed, isPlaying:", isPlaying);
        console.log("CavaModule: Config path:", `${Common.FileUtils.trimFileProtocol(Common.Directories.scriptPath)}/cava/raw_output_config.txt`);
    }

    onIsPlayingChanged: {
        console.log("CavaModule: isPlaying changed to:", isPlaying);
    }

    Process {
        id: cavaProc
        // This correctly uses the 'isPlaying' property from our own module.
        running: true  // Temporarily always run for debugging

        onRunningChanged: {
            console.log("CavaModule: Process running changed to:", cavaProc.running, "isPlaying:", root.isPlaying);
            if (!cavaProc.running) {
                root.visualizerPoints = [];
            }
        }

        command: ["cava", "-p", `${Common.FileUtils.trimFileProtocol(Common.Directories.scriptPath)}/cava/raw_output_config.txt`]

        onExited: (exitCode, exitStatus) => {
            console.log("CavaModule: Process exited with code:", exitCode, "status:", exitStatus);
        }

        stderr: SplitParser {
            onRead: data => {
                console.log("CavaModule: Error:", data);
            }
        }

        stdout: SplitParser {
            onRead: data => {
                let points = data.split(";").map(p => parseFloat(p.trim())).filter(p => !isNaN(p));
                console.log("CavaModule: Received", points.length, "data points");
                root.visualizerPoints = points;
            }
        }
    }

    // --- Visuals (No changes here) ---
    width: 30
    height: 20

    // Temporary background for debugging
    Rectangle {
        anchors.fill: parent
        color: "red"
        opacity: 0.3
    }

    Row {
        anchors.centerIn: parent; spacing: 2
        Repeater {
            model: root.visualizerPoints.length
            Rectangle {
                width: 3
                height: {
                    const point = root.visualizerPoints[index] || 0;
                    const maxHeight = 16;
                    const minHeight = 4;
                    return minHeight + (point * (maxHeight - minHeight));
                }
                radius: 1.5
                color: root.isPlaying ? "#1db954" : "#535353"
                anchors.verticalCenter: parent.verticalCenter
                Behavior on height { NumberAnimation { duration: 80; easing.type: Easing.OutCubic } }
                Behavior on color { ColorAnimation { duration: 400 } }
            }
        }
    }
}